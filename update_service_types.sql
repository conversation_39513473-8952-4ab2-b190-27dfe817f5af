-- Update script to remove numbers from service types
-- This script updates the existing database to remove the numbered prefixes from service types

-- Update service types to remove numbers
UPDATE eodb_master_data_final 
SET value = 'Pre-establishment' 
WHERE value = '1. Pre-establishment' 
AND type_id = (SELECT id FROM eodb_master_data_types_final WHERE type_name = 'service_types');

UPDATE eodb_master_data_final 
SET value = 'Pre-operational' 
WHERE value = '2. Pre-operational' 
AND type_id = (SELECT id FROM eodb_master_data_types_final WHERE type_name = 'service_types');

-- Update step flow dependencies to use new service type names
UPDATE eodb_step_flow_final 
SET dependent_on_value = 'Pre-establishment' 
WHERE dependent_on_value = '1. Pre-establishment';

UPDATE eodb_step_flow_final 
SET dependent_on_value = 'Pre-operational' 
WHERE dependent_on_value = '2. Pre-operational';

-- Verify the changes
SELECT 'Service Types:' as table_name, value 
FROM eodb_master_data_final 
WHERE type_id = (SELECT id FROM eodb_master_data_types_final WHERE type_name = 'service_types')
ORDER BY order_num;

SELECT 'Step Flow Dependencies:' as table_name, dependent_on_value 
FROM eodb_step_flow_final 
WHERE dependent_on_value IN ('Pre-establishment', 'Pre-operational');

-- Complete Dynamic Database Setup for EoDB Chatbot
-- This script creates a fully dynamic chatbot system where all options and flows are database-driven
-- Execute these SQL commands in your PostgreSQL database

-- =====================================================
-- STEP 1: CREATE CORE TABLES
-- =====================================================

-- Create eodb_master_data_types_final table
CREATE TABLE IF NOT EXISTS eodb_master_data_types_final (
    id SERIAL PRIMARY KEY,
    type_name VARCHAR(50) UNIQUE NOT NULL,
    display_name VARCHAR(200) NOT NULL,
    description TEXT,
    created_at TIMESTAMP DEFAULT NOW(),
    updated_at TIMESTAMP DEFAULT NOW()
);

-- Create eodb_master_data_final table
CREATE TABLE IF NOT EXISTS eodb_master_data_final (
    id SERIAL PRIMARY KEY,
    type_id INTEGER REFERENCES eodb_master_data_types_final(id) ON DELETE CASCADE,
    value TEXT NOT NULL,
    order_num INTEGER DEFAULT 0,
    is_active BOOLEAN DEFAULT TRUE,
    created_at TIMESTAMP DEFAULT NOW(),
    updated_at TIMESTAMP DEFAULT NOW()
);

-- Create eodb_step_flow_final table for dynamic flow control
CREATE TABLE IF NOT EXISTS eodb_step_flow_final (
    id SERIAL PRIMARY KEY,
    step_number INTEGER NOT NULL,
    type_id INTEGER REFERENCES eodb_master_data_types_final(id) ON DELETE CASCADE,
    next_step INTEGER,
    response_type VARCHAR(20) NOT NULL CHECK (response_type IN ('options', 'form', 'text')),
    input_caption TEXT NOT NULL,
    dependent_on_step INTEGER,
    dependent_on_value TEXT,
    created_at TIMESTAMP DEFAULT NOW(),
    updated_at TIMESTAMP DEFAULT NOW(),
    UNIQUE(step_number, dependent_on_step, dependent_on_value)
);

-- Create session table
CREATE TABLE IF NOT EXISTS user_session_eodb_data_tab (
    session_id VARCHAR PRIMARY KEY,
    sector VARCHAR,
    investment VARCHAR,
    selected_service VARCHAR,
    time TIMESTAMP DEFAULT NOW()
);

-- =====================================================
-- STEP 2: INSERT MASTER DATA TYPES
-- =====================================================

INSERT INTO eodb_master_data_types_final (type_name, display_name, description) VALUES
('main_options', 'I can help you with the following services:', 'Main service options'),
('sectors', 'Choose sector or industry:', 'Industry sectors/categories'),
('investments', 'What is the total investment value for all the units in your organisation?', 'Investment amount ranges'),
('service_types', 'Select application type:', 'Service type categories'),
('pre_establishment', 'Select the Pre-establishment service:', 'Pre-establishment services'),
('pre_operation', 'Select the Pre-operation service:', 'Pre-operational services'),
('general_query', 'Please enter your query:', 'General query input')
ON CONFLICT (type_name) DO NOTHING;

-- =====================================================
-- STEP 3: INSERT MAIN OPTIONS DATA
-- =====================================================

INSERT INTO eodb_master_data_final (type_id, value, order_num)
SELECT id, unnest(ARRAY[
    '1. Apply for licence/clearance',
    '2. Know application status',
    '3. Raise any query / Grievance',
    '4. FAQ'
]), generate_series(1, 4)
FROM eodb_master_data_types_final WHERE type_name = 'main_options'
ON CONFLICT DO NOTHING;

-- =====================================================
-- STEP 4: INSERT SECTORS DATA
-- =====================================================

INSERT INTO eodb_master_data_final (type_id, value, order_num)
SELECT id, unnest(ARRAY[
    '1. Agriculture & Cooperation',
    '2. Animal Husbandry & Fishing',
    '3. Art & Culture',
    '4. Chemicals & Fertilizers',
    '5. Coal & Mine',
    '6. Commerce & Industry',
    '7. Communications & Information Technology',
    '8. Defence',
    '9. Education & Training',
    '10. Employment & Labour',
    '11. Energy & Power',
    '12. Environment & Natural Resources',
    '13. Finance, Banking & Insurance',
    '14. Food & Public Distribution',
    '15. Forestry & Wildlife',
    '16. Governance & Administration',
    '17. Health & Family Welfare',
    '18. Home Affairs & National Security',
    '19. Housing & Urban Development',
    '20. Information & Broadcasting',
    '21. International Affairs',
    '22. Law & Justice',
    '23. People & Organisations',
    '24. Petroleum, Oil & Natural Gas',
    '25. Rural Development & Panchayati Raj',
    '26. Science, Technology & Research',
    '27. Social Justice & Empowerment',
    '28. Tourism',
    '29. Transport & Infrastructure',
    '30. Youth Affairs & Sports',
    '31. Others'
]), generate_series(1, 31)
FROM eodb_master_data_types_final WHERE type_name = 'sectors'
ON CONFLICT DO NOTHING;

-- =====================================================
-- STEP 5: INSERT INVESTMENTS DATA
-- =====================================================

INSERT INTO eodb_master_data_final (type_id, value, order_num)
SELECT id, unnest(ARRAY[
    '1. Less than INR 2.5 Cr.',
    '2. Between INR 2.5 Cr to 25 Cr.',
    '3. Between INR 25 Cr. To INR 125 Cr.',
    '4. More than INR 125 Cr.',
    '5. Others'
]), generate_series(1, 5)
FROM eodb_master_data_types_final WHERE type_name = 'investments'
ON CONFLICT DO NOTHING;

-- =====================================================
-- STEP 6: INSERT SERVICE TYPES DATA
-- =====================================================

INSERT INTO eodb_master_data_final (type_id, value, order_num)
SELECT id, unnest(ARRAY[
    'Pre-establishment',
    'Pre-operational'
]), generate_series(1, 2)
FROM eodb_master_data_types_final WHERE type_name = 'service_types'
ON CONFLICT DO NOTHING;

-- =====================================================
-- STEP 7: INSERT PRE-ESTABLISHMENT SERVICES
-- =====================================================

INSERT INTO eodb_master_data_final (type_id, value, order_num)
SELECT id, unnest(ARRAY[
    '1. Conversion of Land Use',
    '2. Building Plan Approval (WBIDC)',
    '3. Building Plan Approval (WBIIDC)',
    '4. Building Plan Approval (WBEIDC)',
    '5. Fire Safety Recommendation',
    '6. Allocation/Permission for Surface Water if Surface Water is the Source',
    '7. Land Allocation (IDC)',
    '8. Consent to Establish under Water and Air Act',
    '9. Tree Felling & Tree Transit Permission',
    '10. Approval of Factory Plan under the Factories Act 1948',
    '11. Mutation of Land',
    '12. Registration of Property',
    '13. Drug License (Retail)',
    '14. Drug License (Wholesale)',
    '15. Temporary Electricity Connection (WBSEDCL)',
    '16. Permission for Extraction of Ground Water'
]), generate_series(1, 16)
FROM eodb_master_data_types_final WHERE type_name = 'pre_establishment'
ON CONFLICT DO NOTHING;

-- =====================================================
-- STEP 8: INSERT PRE-OPERATIONAL SERVICES
-- =====================================================

INSERT INTO eodb_master_data_final (type_id, value, order_num)
SELECT id, unnest(ARRAY[
    '1. Building Occupancy Certificate',
    '2. NOC (Fire Safety Certificate) from Fire & ES Dept.',
    '3. Fire License',
    '4. Trade License',
    '5. Consent to Operate under Water and Air Act',
    '6. Electricity Connection',
    '7. Registration under Boiler Act, 1923',
    '8. Factory License under Factories Act 1948',
    '9. Registration under The Shops and Establishment Act',
    '10. Profession Tax Registration',
    '11. ESI Registration for Employer',
    '12. EPF Registration',
    '13. TAN',
    '14. GST'
]), generate_series(1, 14)
FROM eodb_master_data_types_final WHERE type_name = 'pre_operation'
ON CONFLICT DO NOTHING;

-- =====================================================
-- STEP 9: INSERT STEP FLOW CONFIGURATION
-- =====================================================

INSERT INTO eodb_step_flow_final (step_number, type_id, next_step, response_type, input_caption, dependent_on_step, dependent_on_value) VALUES
-- Step 1: Choose main option
(1, (SELECT id FROM eodb_master_data_types_final WHERE type_name = 'main_options'), 2, 'options', 'I can help you with the following services:', NULL, NULL),

-- Step 2: Service types (conditional on licence/clearance selection)
(2, (SELECT id FROM eodb_master_data_types_final WHERE type_name = 'service_types'), 3, 'options', 'Select application type:', 1, '1. Apply for licence/clearance'),

-- Step 2: Sectors (conditional on other main options)
(2, (SELECT id FROM eodb_master_data_types_final WHERE type_name = 'sectors'), 3, 'options', 'Choose sector or industry:', 1, '2. Know application status'),
(2, (SELECT id FROM eodb_master_data_types_final WHERE type_name = 'sectors'), 3, 'options', 'Choose sector or industry:', 1, '3. Raise any query / Grievance'),
(2, (SELECT id FROM eodb_master_data_types_final WHERE type_name = 'sectors'), 3, 'options', 'Choose sector or industry:', 1, '4. FAQ'),

-- Step 3: Pre-establishment services (conditional on Pre-establishment selection)
(3, (SELECT id FROM eodb_master_data_types_final WHERE type_name = 'pre_establishment'), 4, 'options', 'Select the Pre-establishment service:', 2, 'Pre-establishment'),

-- Step 3: Pre-operational services (conditional on Pre-operational selection)
(3, (SELECT id FROM eodb_master_data_types_final WHERE type_name = 'pre_operation'), 4, 'options', 'Select the Pre-operation service:', 2, 'Pre-operational'),

-- Step 3: Investments (conditional on sector selection for other flows)
(3, (SELECT id FROM eodb_master_data_types_final WHERE type_name = 'investments'), 4, 'options', 'What is the total investment value for all the units in your organisation?', NULL, NULL)

ON CONFLICT (step_number, dependent_on_step, dependent_on_value) DO NOTHING;

-- =====================================================
-- STEP 10: CREATE INDEXES FOR PERFORMANCE
-- =====================================================

CREATE INDEX IF NOT EXISTS idx_eodb_master_data_final_type_id ON eodb_master_data_final(type_id);
CREATE INDEX IF NOT EXISTS idx_eodb_master_data_final_active ON eodb_master_data_final(is_active);
CREATE INDEX IF NOT EXISTS idx_eodb_master_data_final_order ON eodb_master_data_final(order_num);
CREATE INDEX IF NOT EXISTS idx_eodb_step_flow_final_step_number ON eodb_step_flow_final(step_number);
CREATE INDEX IF NOT EXISTS idx_eodb_step_flow_final_dependent ON eodb_step_flow_final(dependent_on_step, dependent_on_value);
CREATE INDEX IF NOT EXISTS idx_session_session_id ON user_session_eodb_data_tab(session_id);

-- =====================================================
-- STEP 11: CREATE VIEWS FOR EASY QUERYING
-- =====================================================

CREATE OR REPLACE VIEW v_eodb_step_flow_details_final AS
SELECT
    sf.step_number,
    sf.response_type,
    sf.input_caption,
    sf.next_step,
    sf.dependent_on_step,
    sf.dependent_on_value,
    mdt.type_name,
    mdt.display_name,
    mdt.description
FROM eodb_step_flow_final sf
JOIN eodb_master_data_types_final mdt ON sf.type_id = mdt.id
ORDER BY sf.step_number, sf.dependent_on_step, sf.dependent_on_value;

CREATE OR REPLACE VIEW v_eodb_master_data_with_types_final AS
SELECT
    md.id,
    md.value,
    md.order_num,
    md.is_active,
    mdt.type_name,
    mdt.display_name as type_display_name,
    mdt.description as type_description
FROM eodb_master_data_final md
JOIN eodb_master_data_types_final mdt ON md.type_id = mdt.id
WHERE md.is_active = TRUE
ORDER BY mdt.type_name, md.order_num, md.value;

-- =====================================================
-- STEP 12: CREATE DYNAMIC CONFIGURATION TABLES
-- =====================================================

-- Create table for system configuration
CREATE TABLE IF NOT EXISTS eodb_system_config_final (
    id SERIAL PRIMARY KEY,
    config_key VARCHAR(100) UNIQUE NOT NULL,
    config_value TEXT NOT NULL,
    config_type VARCHAR(50) DEFAULT 'text' CHECK (config_type IN ('text', 'json', 'number', 'boolean')),
    description TEXT,
    is_active BOOLEAN DEFAULT TRUE,
    created_at TIMESTAMP DEFAULT NOW(),
    updated_at TIMESTAMP DEFAULT NOW()
);

-- Create table for greeting messages
CREATE TABLE IF NOT EXISTS eodb_greeting_messages_final (
    id SERIAL PRIMARY KEY,
    message_key VARCHAR(100) UNIQUE NOT NULL,
    message_text TEXT NOT NULL,
    message_type VARCHAR(50) DEFAULT 'initial' CHECK (message_type IN ('initial', 'welcome', 'error', 'success', 'info', 'prompt')),
    language VARCHAR(10) DEFAULT 'en',
    is_active BOOLEAN DEFAULT TRUE,
    created_at TIMESTAMP DEFAULT NOW(),
    updated_at TIMESTAMP DEFAULT NOW()
);

-- Create table for keyword redirects
CREATE TABLE IF NOT EXISTS eodb_keyword_redirects_final (
    id SERIAL PRIMARY KEY,
    keyword VARCHAR(100) NOT NULL,
    redirect_to_step INTEGER NOT NULL,
    redirect_to_option VARCHAR(200),
    redirect_message TEXT,
    is_case_sensitive BOOLEAN DEFAULT FALSE,
    priority INTEGER DEFAULT 0,
    is_active BOOLEAN DEFAULT TRUE,
    created_at TIMESTAMP DEFAULT NOW(),
    updated_at TIMESTAMP DEFAULT NOW()
);

-- Create table for special commands (exit, restart, etc.)
CREATE TABLE IF NOT EXISTS eodb_special_commands_final (
    id SERIAL PRIMARY KEY,
    command VARCHAR(50) UNIQUE NOT NULL,
    command_type VARCHAR(50) NOT NULL CHECK (command_type IN ('exit', 'restart', 'redirect', 'action')),
    response_message TEXT,
    action_data JSON,
    is_case_sensitive BOOLEAN DEFAULT FALSE,
    is_active BOOLEAN DEFAULT TRUE,
    created_at TIMESTAMP DEFAULT NOW(),
    updated_at TIMESTAMP DEFAULT NOW()
);

-- Create table for option status (enabled/disabled)
CREATE TABLE IF NOT EXISTS eodb_option_status_final (
    id SERIAL PRIMARY KEY,
    option_key VARCHAR(100) UNIQUE NOT NULL,
    option_name VARCHAR(200) NOT NULL,
    is_enabled BOOLEAN DEFAULT TRUE,
    disabled_message TEXT,
    order_num INTEGER DEFAULT 0,
    is_active BOOLEAN DEFAULT TRUE,
    created_at TIMESTAMP DEFAULT NOW(),
    updated_at TIMESTAMP DEFAULT NOW()
);

-- =====================================================
-- STEP 13: INSERT DYNAMIC CONFIGURATION DATA
-- =====================================================

-- Insert system configuration
INSERT INTO eodb_system_config_final (config_key, config_value, config_type, description) VALUES
('chatbot_name', 'AI Sanlaap', 'text', 'Name of the chatbot'),
('api_timeout', '30', 'number', 'API timeout in seconds'),
('max_session_duration', '3600', 'number', 'Maximum session duration in seconds'),
('enable_logging', 'true', 'boolean', 'Enable/disable logging'),
('default_language', 'english', 'text', 'Default language for responses'),
('caf_api_url', 'http://localhost:8000/api/caf', 'text', 'CAF API endpoint URL'),
('rag_api_url', 'http://localhost:8000/api/rag', 'text', 'RAG API endpoint URL')
ON CONFLICT (config_key) DO NOTHING;

-- Insert greeting messages
INSERT INTO eodb_greeting_messages_final (message_key, message_text, message_type, language) VALUES
('initial_greeting', 'Namaskar ! I am your Virtual Assistant for AI Sanlaap !', 'initial', 'en'),
('service_help_text', 'I can help you with the following services:', 'initial', 'en'),
('general_query_prompt', 'If you have any other queries, you may type here ........', 'initial', 'en'),
('coming_soon_message', 'This feature is coming soon. Please use the text box below to ask your queries.', 'info', 'en'),
('exit_message', 'Thank you for using AI Sanlaap! The chatbot session has been ended.', 'success', 'en'),
('restart_message', 'Session restarted. Welcome back!', 'success', 'en'),
('error_message', 'Sorry, something went wrong. Please try again.', 'error', 'en'),
('caf_prompt', 'Please enter your CAF number:', 'prompt', 'en'),
('otp_prompt', 'Please enter the OTP you received:', 'prompt', 'en')
ON CONFLICT (message_key) DO NOTHING;

-- Insert keyword redirects
INSERT INTO eodb_keyword_redirects_final (keyword, redirect_to_step, redirect_to_option, redirect_message, priority) VALUES
('application status', 2, '2. Know application status', 'I''ll help you check your application status. Please enter your CAF number:', 10),
('licence', 2, '1. Apply for licence/clearance', 'I''ll help you with licence/clearance applications. Please select the application type:', 9),
('clearance', 2, '1. Apply for licence/clearance', 'I''ll help you with licence/clearance applications. Please select the application type:', 9),
('status', 2, '2. Know application status', 'I''ll help you check your application status. Please enter your CAF number:', 8),
('apply', 2, '1. Apply for licence/clearance', 'I''ll help you with licence/clearance applications. Please select the application type:', 7)
ON CONFLICT DO NOTHING;

-- Insert special commands
INSERT INTO eodb_special_commands_final (command, command_type, response_message, action_data) VALUES
('exit', 'exit', 'Thank you for using AI Sanlaap! The chatbot session has been ended.', '{"action": "end_session", "step": 0}'),
('restart', 'restart', 'Session restarted. Welcome back!', '{"action": "restart_session", "step": 1, "clear_session": true}'),
('help', 'action', 'Here are the available commands: exit, restart, help', '{"action": "show_help"}'),
('menu', 'redirect', 'Returning to main menu...', '{"action": "goto_main_menu", "step": 1}')
ON CONFLICT (command) DO NOTHING;

-- Insert option status
INSERT INTO eodb_option_status_final (option_key, option_name, is_enabled, disabled_message, order_num) VALUES
('apply_licence', '1. Apply for licence/clearance', true, '', 1),
('know_status', '2. Know application status', true, '', 2),
('raise_query', '3. Raise any query / Grievance', false, 'This feature is coming soon. Please use the text box below to ask your queries.', 3),
('faq', '4. FAQ', false, 'This feature is coming soon. Please use the text box below to ask your queries.', 4)
ON CONFLICT (option_key) DO NOTHING;

-- =====================================================
-- STEP 14: CREATE INDEXES FOR DYNAMIC CONFIGURATION TABLES
-- =====================================================

-- Indexes for dynamic configuration tables
CREATE INDEX IF NOT EXISTS idx_eodb_system_config_final_key ON eodb_system_config_final(config_key);
CREATE INDEX IF NOT EXISTS idx_eodb_system_config_final_active ON eodb_system_config_final(is_active);
CREATE INDEX IF NOT EXISTS idx_eodb_greeting_messages_final_key ON eodb_greeting_messages_final(message_key);
CREATE INDEX IF NOT EXISTS idx_eodb_greeting_messages_final_type ON eodb_greeting_messages_final(message_type);
CREATE INDEX IF NOT EXISTS idx_eodb_keyword_redirects_final_keyword ON eodb_keyword_redirects_final(keyword);
CREATE INDEX IF NOT EXISTS idx_eodb_keyword_redirects_final_priority ON eodb_keyword_redirects_final(priority DESC);
CREATE INDEX IF NOT EXISTS idx_eodb_special_commands_final_command ON eodb_special_commands_final(command);
CREATE INDEX IF NOT EXISTS idx_eodb_special_commands_final_type ON eodb_special_commands_final(command_type);
CREATE INDEX IF NOT EXISTS idx_eodb_option_status_final_key ON eodb_option_status_final(option_key);
CREATE INDEX IF NOT EXISTS idx_eodb_option_status_final_order ON eodb_option_status_final(order_num);

-- =====================================================
-- STEP 15: VERIFICATION QUERIES
-- =====================================================

-- Count records in each table
SELECT 'eodb_master_data_types_final' as table_name, count(*) as record_count FROM eodb_master_data_types_final
UNION ALL
SELECT 'eodb_master_data_final', count(*) FROM eodb_master_data_final
UNION ALL
SELECT 'eodb_step_flow_final', count(*) FROM eodb_step_flow_final
UNION ALL
SELECT 'user_session_eodb_data_tab', count(*) FROM user_session_eodb_data_tab
UNION ALL
SELECT 'eodb_system_config_final', count(*) FROM eodb_system_config_final
UNION ALL
SELECT 'eodb_greeting_messages_final', count(*) FROM eodb_greeting_messages_final
UNION ALL
SELECT 'eodb_keyword_redirects_final', count(*) FROM eodb_keyword_redirects_final
UNION ALL
SELECT 'eodb_special_commands_final', count(*) FROM eodb_special_commands_final
UNION ALL
SELECT 'eodb_option_status_final', count(*) FROM eodb_option_status_final;

-- Show step flow configuration
SELECT * FROM v_eodb_step_flow_details_final;

-- Show all master data organized by type
SELECT type_name, count(*) as option_count FROM v_eodb_master_data_with_types_final GROUP BY type_name ORDER BY type_name;
